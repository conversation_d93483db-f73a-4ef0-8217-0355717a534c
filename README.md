# 光存储MQTT服务

这是一个用于处理MQTT消息的Spring Boot应用程序，具有异步处理能力。

## 功能特性

- 支持订阅多个MQTT主题
- 异步消息处理
- 发布消息到MQTT主题
- 用于管理订阅和查看已处理消息的REST API

## 环境要求

- Java 21
- Maven
- MQTT代理服务器（如Mosquitto、HiveMQ等）

## 配置说明

应用程序可以通过`application.properties`文件进行配置：

```properties
# MQTT配置
mqtt.broker.url=tcp://localhost:1883
mqtt.client.id=optical-storage-client
mqtt.username=
mqtt.password=
mqtt.default.topic=optical/storage/data
mqtt.default.qos=1
mqtt.connection.timeout=30
mqtt.keep.alive.interval=60
mqtt.completion.timeout=30000

# 异步任务执行器配置
async.executor.core.pool.size=5
async.executor.max.pool.size=10
async.executor.queue.capacity=25
async.executor.thread.name.prefix=mqtt-async-
```

## 构建应用

```bash
./mvnw clean package
```

## 运行应用

```bash
./mvnw spring-boot:run
```

或在构建后运行：

```bash
java -jar target/optical-storage-0.0.1-SNAPSHOT.jar
```

## API接口说明

### MQTT操作

#### 订阅主题

```
POST /api/mqtt/subscribe?topic={topic}
```

#### 取消订阅主题

```
POST /api/mqtt/unsubscribe?topic={topic}
```

#### 发布消息

```
POST /api/mqtt/publish?topic={topic}&message={message}
```

#### 获取订阅列表

```
GET /api/mqtt/subscriptions
```

### 消息处理

#### 获取指定主题的消息

```
GET /api/mqtt/messages?topic={topic}
```

#### 获取所有包含消息的主题

```
GET /api/mqtt/topics
```

#### 清除所有消息

```
DELETE /api/mqtt/messages
```

## 系统架构

应用程序由以下组件构成：

1. **MQTT配置**：设置MQTT代理服务器连接
2. **异步配置**：配置用于异步处理的线程池
3. **MQTT消息处理器**：接收消息并委托给处理服务
4. **消息处理服务**：异步处理消息
5. **MQTT订阅管理器**：管理主题订阅
6. **MQTT发布器**：向主题发布消息
7. **REST控制器**：提供MQTT操作的API接口

