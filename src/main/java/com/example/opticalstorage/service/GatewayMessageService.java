package com.example.opticalstorage.service;

import com.example.opticalstorage.model.mqtt.*;
import com.example.opticalstorage.util.MessageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 网关消息服务，处理各种类型的消息
 */
@Service
public class GatewayMessageService {

    private static final Logger logger = LoggerFactory.getLogger(GatewayMessageService.class);

    private final MqttPublisher mqttPublisher;
    private final MessageFormatService messageFormatService;

    @Value("${gateway.sale.id}")
    private String saleId;

    @Value("${gateway.gate.id}")
    private String gateId;

    @Value("${mqtt.publish.topic}")
    private String publishTopic;

    // 存储控制命令的响应
    private final Map<String, CompletableFuture<ControlAckMessage>> controlResponses = new ConcurrentHashMap<>();

    // 存储遥调命令的响应
    private final Map<String, CompletableFuture<SetAckMessage>> setResponses = new ConcurrentHashMap<>();

    // 存储多值写入命令的响应
    private final Map<String, CompletableFuture<MultValueSetAckMessage>> multValueSetResponses = new ConcurrentHashMap<>();

    // 存储储能策略的响应
    private final Map<String, CompletableFuture<StorageStrategyAckMessage>> storageStrategyResponses = new ConcurrentHashMap<>();

    public GatewayMessageService(MqttPublisher mqttPublisher, MessageFormatService messageFormatService) {
        this.mqttPublisher = mqttPublisher;
        this.messageFormatService = messageFormatService;
    }

    /**
     * 处理接收到的消息
     *
     * @param topic   消息主题
     * @param payload 消息内容
     */
    @Async("mqttTaskExecutor")
    public void processMessage(String topic, String payload) {
        logger.info("Processing message from topic: {}", topic);

        try {
            // 解析消息格式
            String parsedPayload = messageFormatService.smartParseMessage(payload);
            BaseMessage message = MessageUtils.parseMessage(parsedPayload);

            if (message == null) {
                logger.warn("Failed to parse message: {}", payload);
                return;
            }

            switch (message.getType()) {
                case "report":
                    processReportMessage((ReportMessage) message);
                    break;
                case "alarm":
                    processAlarmMessage((AlarmMessage) message);
                    break;
                case "heart_beat":
                    processHeartBeatMessage((HeartBeatMessage) message);
                    break;
                case "control":
                    processControlMessage((ControlMessage) message);
                    break;
                case "control_ack":
                    processControlAckMessage((ControlAckMessage) message);
                    break;
                case "set":
                    processSetMessage((SetMessage) message);
                    break;
                case "set_ack":
                    processSetAckMessage((SetAckMessage) message);
                    break;
                case "callRead":
                    processCallReadMessage((CallReadMessage) message);
                    break;
                case "callAck":
                    processCallAckMessage((CallAckMessage) message);
                    break;
                case "getCurrentData":
                    processGetCurrentDataMessage((GetCurrentDataMessage) message);
                    break;
                case "multvalueset":
                    processMultValueSetMessage((MultValueSetMessage) message);
                    break;
                case "multvalueset_ack":
                    processMultValueSetAckMessage((MultValueSetAckMessage) message);
                    break;
                case "storageStrategy":
                    processStorageStrategyMessage((StorageStrategyMessage) message);
                    break;
                case "storageStrategy_ack":
                    processStorageStrategyAckMessage((StorageStrategyAckMessage) message);
                    break;
                default:
                    logger.warn("Unknown message type: {}", message.getType());
            }
        } catch (Exception e) {
            logger.error("Error processing message", e);
        }
    }

    /**
     * 处理实时数据上报消息
     */
    private void processReportMessage(ReportMessage message) {
        logger.info("Processing report message: {}", message);
        // 实际应用中，这里可以将数据存储到数据库或进行其他处理
    }

    /**
     * 处理报警消息
     */
    private void processAlarmMessage(AlarmMessage message) {
        logger.info("Processing alarm message: {}", message);
        // 实际应用中，这里可以触发报警通知或进行其他处理
    }

    /**
     * 处理心跳校时消息
     */
    private void processHeartBeatMessage(HeartBeatMessage message) {
        logger.info("Processing heart beat message: {}", message);

        if ("notify".equals(message.getOperation())) {
            // 收到网关的心跳请求，返回服务器时间
            HeartBeatMessage response = new HeartBeatMessage(
                    message.getSaleId(),
                    message.getGateId(),
                    MessageUtils.getCurrentTime(),
                    "time"
            );

            publishFormattedMessage(publishTopic, response);
        }
    }

    /**
     * 处理控制命令消息
     */
    private void processControlMessage(ControlMessage message) {
        logger.info("Processing control message: {}", message);

        // 创建一个 Future 用于等待响应
        CompletableFuture<ControlAckMessage> future = new CompletableFuture<>();
        controlResponses.put(message.getCuuid(), future);

        // 实际应用中，这里应该执行实际的控制操作
        // 这里模拟一个成功的响应
        ControlAckMessage response = new ControlAckMessage(
                message.getSaleId(),
                message.getGateId(),
                MessageUtils.getCurrentTime(),
                message.getCuuid(),
                message.getMeterId(),
                message.getName(),
                message.getFunctionId(),
                message.getValue(),
                "success",
                ""
        );

        // 发送响应
        publishFormattedMessage(publishTopic, response);
    }

    /**
     * 处理控制反馈消息
     */
    private void processControlAckMessage(ControlAckMessage message) {
        logger.info("Processing control ack message: {}", message);

        // 完成对应的 Future
        CompletableFuture<ControlAckMessage> future = controlResponses.remove(message.getCuuid());
        if (future != null) {
            future.complete(message);
        }
    }

    /**
     * 处理遥调命令消息
     */
    private void processSetMessage(SetMessage message) {
        logger.info("Processing set message: {}", message);

        // 创建一个 Future 用于等待响应
        CompletableFuture<SetAckMessage> future = new CompletableFuture<>();
        setResponses.put(message.getCuuid(), future);

        // 实际应用中，这里应该执行实际的遥调操作
        // 这里模拟一个成功的响应
        SetAckMessage response = new SetAckMessage(
                message.getSaleId(),
                message.getGateId(),
                MessageUtils.getCurrentTime(),
                message.getCuuid(),
                message.getMeterId(),
                message.getName(),
                message.getFunctionId(),
                message.getValue(),
                "success",
                ""
        );

        // 发送响应
        publishFormattedMessage(publishTopic, response);
    }

    /**
     * 处理遥调反馈消息
     */
    private void processSetAckMessage(SetAckMessage message) {
        logger.info("Processing set ack message: {}", message);

        // 完成对应的 Future
        CompletableFuture<SetAckMessage> future = setResponses.remove(message.getCuuid());
        if (future != null) {
            future.complete(message);
        }
    }

    /**
     * 处理召读历史数据消息
     */
    private void processCallReadMessage(CallReadMessage message) {
        logger.info("Processing call read message: {}", message);

        // 发送召读响应
        CallAckMessage response = new CallAckMessage(
                message.getSaleId(),
                message.getGateId(),
                MessageUtils.getCurrentTime()
        );

        publishFormattedMessage(publishTopic, response);

        // 实际应用中，这里应该查询历史数据并发送
        // 这里模拟发送一些历史数据
        sendHistoricalData(message);
    }

    /**
     * 处理召读响应消息
     */
    private void processCallAckMessage(CallAckMessage message) {
        logger.info("Processing call ack message: {}", message);
        // 实际应用中，这里可以记录召读响应
    }

    /**
     * 处理获取当前数据消息
     */
    private void processGetCurrentDataMessage(GetCurrentDataMessage message) {
        logger.info("Processing get current data message: {}", message);

        // 发送当前数据
        sendCurrentData(message);
    }

    /**
     * 处理多值写入消息
     */
    private void processMultValueSetMessage(MultValueSetMessage message) {
        logger.info("Processing mult value set message: {}", message);

        // 创建一个 Future 用于等待响应
        CompletableFuture<MultValueSetAckMessage> future = new CompletableFuture<>();
        multValueSetResponses.put(message.getCuuid(), future);

        // 实际应用中，这里应该执行实际的多值写入操作
        // 这里模拟一个成功的响应
        MultValueSetAckMessage response = new MultValueSetAckMessage(
                message.getSaleId(),
                message.getGateId(),
                MessageUtils.getCurrentTime(),
                message.getCuuid(),
                message.getMeterId(),
                message.getName(),
                message.getFunctionId(),
                "success",
                ""
        );

        // 发送响应
        String responseJson = MessageUtils.toJson(response);
        String formattedResponse = messageFormatService.formatOutboundMessage(responseJson);
        mqttPublisher.publish(publishTopic, formattedResponse);
    }

    /**
     * 处理多值写入反馈消息
     */
    private void processMultValueSetAckMessage(MultValueSetAckMessage message) {
        logger.info("Processing mult value set ack message: {}", message);

        // 完成对应的 Future
        CompletableFuture<MultValueSetAckMessage> future = multValueSetResponses.remove(message.getCuuid());
        if (future != null) {
            future.complete(message);
        }
    }

    /**
     * 处理储能策略参数下发消息
     */
    private void processStorageStrategyMessage(StorageStrategyMessage message) {
        logger.info("Processing storage strategy message: {}", message);

        // 创建一个 Future 用于等待响应
        CompletableFuture<StorageStrategyAckMessage> future = new CompletableFuture<>();
        storageStrategyResponses.put(message.getCuuid(), future);

        // 实际应用中，这里应该执行实际的储能策略配置操作
        // 这里模拟一个成功的响应
        StorageStrategyAckMessage response = new StorageStrategyAckMessage(
                message.getSaleId(),
                message.getGateId(),
                MessageUtils.getCurrentTime(),
                message.getCuuid(),
                "success",
                ""
        );

        // 发送响应
        String responseJson = MessageUtils.toJson(response);
        String formattedResponse = messageFormatService.formatOutboundMessage(responseJson);
        mqttPublisher.publish(publishTopic, formattedResponse);
    }

    /**
     * 处理储能策略反馈消息
     */
    private void processStorageStrategyAckMessage(StorageStrategyAckMessage message) {
        logger.info("Processing storage strategy ack message: {}", message);

        // 完成对应的 Future
        CompletableFuture<StorageStrategyAckMessage> future = storageStrategyResponses.remove(message.getCuuid());
        if (future != null) {
            future.complete(message);
        }
    }

    /**
     * 发送格式化的消息
     */
    private void publishFormattedMessage(String topic, Object message) {
        String json = MessageUtils.toJson(message);
        String formattedMessage = messageFormatService.formatOutboundMessage(json);
        mqttPublisher.publish(topic, formattedMessage);
    }

    /**
     * 发送历史数据
     */
    private void sendHistoricalData(CallReadMessage message) {
        // 模拟发送一些历史数据
        ReportMessage reportMessage = createSampleReportMessage();
        reportMessage.setSaleId(message.getSaleId());
        reportMessage.setGateId(message.getGateId());
        reportMessage.setSource("db"); // 标记为历史数据

        publishFormattedMessage(publishTopic, reportMessage);
    }

    /**
     * 发送当前数据
     */
    private void sendCurrentData(GetCurrentDataMessage message) {
        // 模拟发送当前数据
        ReportMessage reportMessage = createSampleReportMessage();
        reportMessage.setSaleId(message.getSaleId());
        reportMessage.setGateId(message.getGateId());
        reportMessage.setType("getCurrentData"); // 修改类型

        publishFormattedMessage(publishTopic, reportMessage);
    }

    /**
     * 创建示例报告消息
     */
    private ReportMessage createSampleReportMessage() {
        ReportMessage message = new ReportMessage(
                saleId,
                gateId,
                MessageUtils.getCurrentTime(),
                "100",
                "da"
        );

        List<ReportMessage.MeterData> meters = new ArrayList<>();

        // 添加第一个仪表数据
        Map<String, String> values1 = new HashMap<>();
        values1.put("Ua", "233.56");
        values1.put("Ub", "133.21");
        values1.put("Uc", "234.35");
        values1.put("Ia", "3.20");
        values1.put("Ib", "4.87");
        values1.put("Ic", "3.69");
        values1.put("Uab", "432.77");

        ReportMessage.MeterData meter1 = new ReportMessage.MeterData(
                "T1010001",
                "1",
                "仪表 AAA",
                values1
        );

        // 添加第二个仪表数据
        Map<String, String> values2 = new HashMap<>();
        values2.put("Ua", "233.56");
        values2.put("Ub", "133.21");
        values2.put("Uc", "234.35");
        values2.put("Ia", "3.20");
        values2.put("Ib", "4.87");
        values2.put("Ic", "3.69");
        values2.put("Uab", "432.77");

        ReportMessage.MeterData meter2 = new ReportMessage.MeterData(
                "T1010002",
                "1",
                "仪表 BBB",
                values2
        );

        meters.add(meter1);
        meters.add(meter2);
        message.setMeter(meters);

        return message;
    }
}
